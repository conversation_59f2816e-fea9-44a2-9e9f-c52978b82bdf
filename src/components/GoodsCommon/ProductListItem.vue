<template>
  <li class="goods-item" @click="handleItemClick">
    <div class="goods-image">
      <img
        :src="item.showImageUrl"
        :alt="item.name"
        loading="lazy"
        width="85"
        height="85"
      />
    </div>
    <div class="goods-info">
      <div class="goods-info-main">
        <h3 class="goods-title">{{ item.name }}</h3>
        <p class="goods-spec">{{ item.params.join(' ') }}</p>
      </div>
      <div class="goods-info-footer">
        <div class="price-sales">
          <template v-if="isZQ && (item.highPrice || item.lowPrice)">
            <PriceDisplay
              :high-price="item.highPrice"
              :low-price="item.lowPrice"
              range-label="参考价"
              size="small"
              color="orange"
            />
          </template>
          <template v-else>
            <PriceDisplay :price="item.price" size="small" color="orange" />
          </template>
          <span class="sales-count" v-if="!isZQ">销量{{ item.realSaleVolume }}件</span>
        </div>
        <button
          v-if="!isZQ"
          class="cart-btn"
          @click.stop="handleAddCart"
          type="button"
          aria-label="加入购物车"
        >
          <img src="@/static/images/quick-cart.png" alt="" width="25" height="25" />
        </button>
      </div>
    </div>
  </li>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import { getBizCode } from '@utils/curEnv.js'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const { item } = toRefs(props)

const isZQ = computed(() => getBizCode() === 'zq')

const emit = defineEmits(['item-click', 'add-cart'])

const handleItemClick = () => {
  emit('item-click', item.value)
}

const handleAddCart = () => {
  emit('add-cart', item.value)
}
</script>

<style scoped lang="less">
.goods-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  cursor: pointer;

  .goods-image {
    width: 85px;
    height: 85px;
    flex-shrink: 0;
    border-radius: 8px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
  }

  .goods-info {
    flex: 1;
    margin-left: 8px;
    padding-top: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .goods-info-main {
      .goods-title {
        font-size: 13px;
        color: #333;
        margin: 0;
        font-weight: normal;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.5;
      }

      .goods-spec {
        font-size: 11px;
        color: #666;
        line-height: 1.5;
        margin: 4px 0 0 0;
      }
    }

    .goods-info-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 8px;

      .price-sales {
        display: flex;
        align-items: center;

        .sales-count {
          font-size: 11px;
          color: #999;
          margin-left: 12px;
        }
      }

      .cart-btn {
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;

        img {
          width: 25px;
          height: 25px;
        }
      }
    }
  }
}
</style>
