<template>
  <div class="global-filtering" @click="showProvinceDrawer = true" v-if="roleType === '4'">
    <div class="global-filtering-title">省分在售商品</div>
    <img class="global-filtering-icon" src="@/static/images/filtering.png" alt="" srcset="">
  </div>
  <BaseHomeLayout home-class="zq-home" search-placeholder="搜索商品" :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems" :grid-columns="5" :skeleton-states="skeletonStates" @search="handleSearch"
    @banner-click="handleBannerClick" @grid-item-click="handleGridItemClick" @more-click="handleMoreClick">
    <template #main-content>
      <WaterfallSection :waterfall-goods-list="waterfallGoodsList" :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished" :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete" :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        loadMode="scroll" @goods-click="handleGoodsClick" @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender" />

      <!--      TODO: 没有数据要增加提示和样式-->
    </template>
  </BaseHomeLayout>
  <ProvinceServiceSelector v-model="showProvinceDrawer" @confirm="handleSelectionConfirm" />
</template>
<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsCommon/WaterfallSection.vue'
import ProvinceServiceSelector from '@/components/Common/ZQSelectFilter/Index.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getEnterpriseManagerInfo } from '@/utils/zqInfo'
import { useProvinceServiceStore } from '@/store/modules/provinceService.js'


// 使用组合式函数
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallCurrentPage,
  waterfallPageSize,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  getHeaderBannerList,
  getIconList,
  getWaterfallList,
  getWaterfallListWithStore,
  resetWaterfallState,
  getPartionListData
} = useHomeData()

const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 使用 Pinia store
const provinceServiceStore = useProvinceServiceStore()

// 页面特有数据
const typeList = ref([])
const goodsPoolIdSelected = ref('')
const showProvinceDrawer = ref(false)

// 计算属性
const roleType = computed(() => {
  const { roleType: rt = '' } = getEnterpriseManagerInfo() || {}
  return rt
})

// 瀑布流渲染完成处理
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 加载更多瀑布流商品
const handleWaterfallLoadMore = () => {
  if (roleType.value === '4') {
    getWaterfallListWithStore(goodsPoolIdSelected.value, '', true, provinceServiceStore)
  } else {
    getWaterfallList(goodsPoolIdSelected.value, '', true)
  }
}

// 切换商品池
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  // 对于 roleType === '4' 的用户，使用支持 store 的加载方法
  if (roleType.value === '4') {
    getWaterfallListWithStore(id, sortType, false, provinceServiceStore)
  } else {
    getWaterfallList(id, sortType, false)
  }
}

// 处理省份服务商选择确认
const handleSelectionConfirm = (selection) => {
  console.log('确认选择，当前选中的省份ID:', selection.areaId)
  console.log('确认选择，当前选中的服务商ID:', selection.isvId)
  console.log('确认选择，当前选中的省份名称:', selection.provinceName)
  console.log('确认选择，当前选中的服务商名称:', selection.serviceName)

  // 保存选择到 store
  if (selection.areaId) {
    provinceServiceStore.selectProvince(selection.areaId)
  }
  if (selection.isvId) {
    provinceServiceStore.selectService(selection.isvId)
  }

  // 重置瀑布流状态并重新加载商品列表
  resetWaterfallState()

  // 使用支持 store 的加载方法重新加载商品
  getWaterfallListWithStore(goodsPoolIdSelected.value, '', false, provinceServiceStore)

  // 关闭选择器
  showProvinceDrawer.value = false
}

// 初始化页面数据
const initPage = async () => {
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]
    goodsPoolIdSelected.value = recommond.id
    changeGoodsPool(recommond.id)
  }
}

onMounted(() => {
  getHeaderBannerList()
  getIconList(8) // ZQHome 使用 showPage:
  initPage()
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped lang="less">
.zq-home {
  padding-bottom: 48px;
  box-sizing: border-box;
}

.global-filtering {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10px;
  background: #fff;

  .global-filtering-title {
    font-size: 15px;
    color: #5A6066;
  }

  .global-filtering-icon {
    margin-left: 10px;
    width: 18px;
    height: 18px;
  }
}
</style>
