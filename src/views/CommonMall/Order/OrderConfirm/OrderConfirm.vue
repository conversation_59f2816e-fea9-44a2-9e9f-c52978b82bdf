<template>
  <main class="order-confirm">
    <section class="order-confirm__address">
      <AddressSkeleton v-if="addressLoading" />
      <AddressSelectionCard :loading="addressLoading" :address="selectedAddress" :is-complete="isAddressComplete"
        @select="handleSelectAddress" />
    </section>

    <section class="order-confirm__goods">
      <GoodsListSkeleton v-if="goodsLoading" />
      <OrderConfirmGoodsListLayout v-else-if="goodsList.length > 0" :goods-list="goodsList" :image-size="65"
        :min-height="65" :show-actions="false" />
    </section>

    <section v-if="!isZqBiz" class="order-confirm__summary">
      <OrderSummarySkeleton v-if="summaryLoading" />
      <WoCard v-else>
        <InfoRow label="商品总价">
          <template #value>
            <PriceDisplay :price="orderSummary.goodsTotal" size="small" />
          </template>
        </InfoRow>
        <InfoRow label="运费" :value="orderSummary.shippingText" />
        <InfoRow label="慰问活动" :value="orderSummary.activityText"
          :value-class="orderSummary.activityText ? 'order-confirm__activity-text' : 'order-confirm__activity-text order-confirm__activity-text--empty'"
          :show-arrow="true" @click="handleShowQuotaPopup" />
        <InfoRow label="实付款">
          <template #value>
            <PriceDisplay :price="orderSummary.actualPayment" size="small" color="orange" />
          </template>
        </InfoRow>
      </WoCard>
    </section>


    <section v-if="isZqBiz" class="order-confirm__summary">
      <OrderSummarySkeleton v-if="summaryLoading" />
      <WoCard title="企业信息" v-else>
        <InfoRow label="企业名称" :value="enterpriseName" />
        <InfoRow label="采购数量" :value="totalGoodsNum" />
      </WoCard>
    </section>


    <WoActionBarPlaceholder />

    <WoActionBar>
      <div class="order-confirm__submit">
        <div v-if="!isZqBiz" class="order-confirm__submit-info">
          <span class="order-confirm__submit-price">
            <PriceDisplay :price="summaryLoading ? '' : orderSummary.actualPayment" size="large" color="orange" />
          </span>
          <div class="order-confirm__submit-details">
            <span v-if="summaryLoading">额度抵扣 --.-</span>
            <span v-else-if="orderSummary.savingsText">{{ orderSummary.savingsText }}</span>
            <span v-if="summaryLoading">自付金额 --.-</span>
            <span v-else-if="orderSummary.refundText">{{ orderSummary.refundText }}</span>
          </div>
        </div>
        <WoButton type="gradient" size="special" :block="isZqBiz" @click="handleSubmitOrder">
          {{ isZqBiz ? '通知发货' : '提交订单' }}
        </WoButton>
      </div>
    </WoActionBar>

    <AddressQuickSelectionPopup v-model:visible="showAddressPopup" @select="handleAddressSelected"
      @create="handleCreateAddress" @edit="handleEditAddress" />

    <QuotaInfoPopup v-model="showQuotaPopup" :available-activity-quota="availableActivityQuota"
      :un-available-activity-quota="unAvailableActivityQuota" @change="handleQuotaChange" />
  </main>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import { get, isEmpty, debounce, throttle, compact } from 'lodash-es'
import Decimal from 'decimal.js'

import PriceDisplay from '@components/Common/PriceDisplay.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderConfirmGoodsListLayout from '@views/CommonMall/Order/OrderConfirm/components/OrderConfirmGoodsListLayout.vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import InfoRow from '@components/Common/InfoRow.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import AddressSelectionCard from './components/AddressSelectionCard.vue'
import GoodsListSkeleton from './components/GoodsListSkeleton.vue'
import OrderSummarySkeleton from './components/OrderSummarySkeleton.vue'

const AddressQuickSelectionPopup = defineAsyncComponent(() =>
  import('@components/Common/Address/AddressQuickSelectionPopup.vue')
)
const QuotaInfoPopup = defineAsyncComponent(() =>
  import('@components/Common/QuotaInfoPopup/QuotaInfoPopup.vue')
)

import { getBuyNowGoods, getSelectedGoods, getFuLiHuiID, jdAddressCheck } from '@api/index.js'
import { submitOrder, queryAmount, notApplicableQuotaWish } from '@api/interface/order.js'
import { getLimitAreaList } from '@api/interface/goods.js'
import { getBizCode } from '@utils/curEnv.js'
import { buyProductNow, buyProductNowSession, buyProductCart, buyProductCartSession, curDeveloperId, curChannelBiz } from '@utils/storage.js'
import { formSubmit } from 'commonkit'
import { fenToYuan } from '@utils/amount.js'
import { useAlert } from '@/composables/index.js'
import AddressSkeleton from '@views/CommonMall/Order/OrderConfirm/components/AddressSkeleton.vue'
import { getEnterpriseManagerInfo } from '@utils/zqInfo.js'
const $alert = useAlert()
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const cartStore = useNewCartStore()

const JD_GOODS_CODE = 'JD'
const ORDER_TYPE = {
  BUY_NOW: '1',
  CART: '2'
}

const showAddressPopup = ref(false)
const showQuotaPopup = ref(false)
const loading = ref(false)
const addressLoading = ref(true)
const goodsLoading = ref(true)
const summaryLoading = ref(true)
const orderType = ref('')
const orderCartList = ref([])
const cartPriceTotal = ref(0)
const totalFreight = ref(0)
const isJD = ref(false)
const isSubmit = ref(false)
const isRemoveGifts = ref(false)
const orderVoInfo = ref(null)
const dialogShow = ref(false)
const wapay = ref({
  encryptContent: '',
  wapURL: '',
  bizOrderId: ''
})
const developerId = ref('')
const isRegionalPurchase = ref(false)

const availableActivityQuota = ref({})
const unAvailableActivityQuota = ref({})
const activeName = ref('')
const quotaPayment = ref(0)
const selfPayment = ref(0)

const isZqBiz = computed(() => getBizCode() === 'zq')

const enterpriseName = computed(() => {
  const enterpriseInfo = getEnterpriseManagerInfo()
  return enterpriseInfo ? enterpriseInfo.ciName : '-'
})

const totalGoodsNum = computed(() => {
  return goodsList.value.reduce((total, goods) => {
    return total + (goods.quantity || 0)
  }, 0)
})

const selectedAddress = computed(() => {
  const addressInfo = userStore.curAddressInfo
  const { recName = '', recPhone = '', provinceName = '', cityName = '', countyName = '', addrDetail = '' } = addressInfo

  return {
    name: recName,
    phone: recPhone,
    region: compact([provinceName, cityName, countyName]).join(''),
    detailAddress: addrDetail
  }
})

const isAddressComplete = computed(() => {
  const addressInfo = userStore.curAddressInfo
  const requiredFields = ['recName', 'recPhone', 'provinceName', 'cityName', 'countyName', 'addrDetail']
  return requiredFields.every(field => !isEmpty(get(addressInfo, field)))
})

const goodsList = computed(() => {
  return get(orderCartList.value, '[0].goodsList', [])
})

const orderSummary = computed(() => {
  const priceInCents = cartPriceTotal.value * 100

  return {
    goodsTotal: priceInCents,
    shippingText: isJD.value && totalFreight.value ? '运费已分摊至商品金额' : '免运费',
    activityText: activeName.value || '暂无活动',
    actualPayment: priceInCents,
    savingsText: quotaPayment.value >= 0 ? `额度抵扣 ¥${quotaPayment.value}` : '',
    refundText: selfPayment.value >= 0 ? `自付金额 ¥${selfPayment.value}` : ''
  }
})

const processBuyNowData = (data) => {
  const validGoodsList = get(data, 'validGoodsList', [])
  const invalidGoodsList = get(data, 'invalidGoodsList', [])
  const allGoodsList = [...validGoodsList, ...invalidGoodsList]

  const transformedGoodsList = allGoodsList.map(item => {
    const goods = get(item, 'goods', {})
    const sku = get(goods, 'skuList[0]', {})
    return {
      id: `${get(sku, 'goodsId', '')}_${get(sku, 'skuId', '')}`,
      name: get(sku, 'name', ''),
      price: get(item, 'nowPrice', 0),
      quantity: get(item, 'skuNum', 0),
      detailImageUrl: get(sku, 'listImageUrl', ''),
      skuNeedGift: get(item, 'skuNeedGift'),
      rawData: item,
      skuNumInfoList: [{
        sku: {
          ...sku,
          goodsId: get(sku, 'goodsId', ''),
          skuId: get(sku, 'skuId', ''),
          name: get(sku, 'name', ''),
          detailImageUrl: get(sku, 'listImageUrl', ''),
          param: get(sku, 'param', ''),
          param1: get(sku, 'param1', ''),
          param2: get(sku, 'param2', ''),
          param3: get(sku, 'param3', ''),
          param4: get(sku, 'param4', ''),
          price: get(item, 'nowPrice', 0)
        },
        skuNum: get(item, 'skuNum', 0)
      }]
    }
  })

  orderCartList.value = [{ goodsList: transformedGoodsList }]
  cartPriceTotal.value = parseFloat((get(data, 'cartPriceTotal', 0) / 100).toFixed(2))
  totalFreight.value = get(data, 'totalFreight', 0)

  checkJDGoods()
}

const checkJDGoods = () => {
  isJD.value = orderCartList.value.some(goodGroup =>
    get(goodGroup, 'goodsList', []).some(goodsItem =>
      get(goodsItem, 'goods.skuList', []).some(sku =>
        get(sku, 'supplierCode', '').includes(JD_GOODS_CODE)
      )
    )
  )
}

const checkRegionalRestrictions = async () => {
  const curAddrInfo = userStore.curAddressInfo

  const goodsIdList = compact(
    orderCartList.value.flatMap(supplier =>
      get(supplier, 'goodsList', []).map(skuInfo => {
        skuInfo.nosale = false
        return get(skuInfo, 'rawData.cartGoodsId')
      })
    )
  )

  const addressFields = ['provinceId', 'cityId', 'countyId', 'townId']
  const areaInfo = addressFields.reduce((acc, field) => {
    const value = get(curAddrInfo, field)
    if (value) acc[field] = value
    return acc
  }, {})

  if (isEmpty(areaInfo)) return

  const params = {
    area: JSON.stringify(areaInfo),
    goodsIdList
  }

  try {
    showLoadingToast()
    const [err, json] = await getLimitAreaList(params)
    closeToast()

    if (!err) {
      const hasRestrictions = !isEmpty(json)

      orderCartList.value.forEach(supplier => {
        get(supplier, 'goodsList', []).forEach(skuInfo => {
          const cartGoodsId = get(skuInfo, 'rawData.cartGoodsId')
          skuInfo.nosale = hasRestrictions && json.includes(cartGoodsId)
        })
      })

      isRegionalPurchase.value = hasRestrictions

      if (isJD.value && totalFreight.value) {
        showToast('运费已分摊至商品金额')
      }
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    // console.error('检查区域限制失败:', error)
  }
}

const handleBuyNowOrder = async () => {
  const { goodsId, skuId, goodsNum } = route.query
  const addressInfo = JSON.stringify(userStore.curAddressInfo)

  orderType.value = ORDER_TYPE.BUY_NOW

  const cachedData = buyProductNow.get() || buyProductNowSession.get()

  if (cachedData) {
    processBuyNowData(cachedData.data)
    if (cachedData.code !== '0000') {
      showToast(cachedData.msg)
      isSubmit.value = false
      return false
    }
    isSubmit.value = true
    return true
  }

  try {
    showLoadingToast()

    const [res, data] = await getBuyNowGoods({
      goodsId,
      skuId,
      goodsNum,
      addressInfo,
      bizCode: getBizCode('ORDER')
    })

    closeToast()

    if (res.code !== '0000') {
      showToast(res.msg)
      isSubmit.value = false
      return false
    }

    processBuyNowData(data)
    isSubmit.value = true
    return true

  } catch (error) {
    closeToast()
    showToast('加载订单信息失败')
    isSubmit.value = false
    return false
  }
}

const handleCartOrder = async () => {
  const addressInfo = JSON.stringify(userStore.curAddressInfo)
  orderType.value = ORDER_TYPE.CART

  const buyGoodsList = buyProductCart.get() || buyProductCartSession.get()

  if (!buyGoodsList) {
    // console.error('购物车下单缓存数据为空')
    showToast('购物车数据异常')
    isSubmit.value = false
    return false
  }

  try {
    showLoadingToast()

    const [res, data] = await getSelectedGoods({
      bizCode: getBizCode('ORDER'),
      addressInfo,
      buyGoodsList: JSON.stringify(buyGoodsList)
    })

    closeToast()

    if (res.code !== '0000') {
      showToast(res.msg)
      isSubmit.value = false
      return false
    }

    processBuyNowData(data)
    isSubmit.value = true
    return true

  } catch (error) {
    closeToast()
    showToast('加载订单信息失败')
    isSubmit.value = false
    return false
  }
}

const loadAddressData = async () => {
  addressLoading.value = true
  try {
    await userStore.queryDefaultAddr()
  } catch (error) {
    // console.error('加载地址失败:', error)
    showToast('加载地址失败')
  } finally {
    addressLoading.value = false
  }
}

const loadGoodsData = async () => {
  goodsLoading.value = true
  try {
    const { goodsId, skuId, goodsNum } = route.query
    const isDirectBuy = goodsId && skuId && goodsNum

    const success = isDirectBuy ? await handleBuyNowOrder() : await handleCartOrder()

    if (success) {
      if (isJD.value && totalFreight.value) {
        showToast('运费已分摊至商品金额')
      }
      await checkRegionalRestrictions()
    }

    return success
  } catch (error) {
    // console.error('加载商品数据失败:', error)
    showToast('加载商品数据失败')
    return false
  } finally {
    goodsLoading.value = false
  }
}

const loadSummaryData = async () => {
  if (isZqBiz.value) {
    summaryLoading.value = false
    return
  }

  summaryLoading.value = true
  try {
    await getPaymentDetails()
  } catch (error) {
    // console.error('加载订单汇总失败:', error)
    showToast('加载订单汇总失败')
  } finally {
    summaryLoading.value = false
  }
}

const initOrderData = async () => {
  if (loading.value) return

  loading.value = true

  try {
    await loadAddressData()
    await nextTick()

    const goodsSuccess = await loadGoodsData()
    await nextTick()

    await loadSummaryData()

    if (!goodsSuccess) {
      console.warn('商品数据加载失败，但页面仍可使用')
    }

  } catch (error) {
    // console.error('初始化订单失败:', error)
    showToast('初始化订单失败')
  } finally {
    loading.value = false
  }
}

const handleSelectAddress = debounce(() => {
  showAddressPopup.value = true
}, 300)

const addressCheck = async () => {
  showLoadingToast()
  const [err, json] = await jdAddressCheck()
  closeToast()

  if (err) {
    showToast(err.msg)
    return false
  }

  if (!json) {
    $alert({
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存',
      confirmButtonText: '修改地址',
      showCancelButton: true,
      cancelButtonText: '取消',
      messageAlign: 'center',
      onConfirmCallback: () => {
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      },
      onCancelCallback: () => { }
    })

    return false
  }

  return true
}

const handleAddressSelected = async () => {
  showToast('地址选择成功')
  cartStore.query()

  const isPassed = await addressCheck()
  if (!isPassed) return

  const { goodsId, skuId, goodsNum } = route.query
  const info = userStore.curAddressInfo
  const addressFields = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName', 'townId', 'townName']
  const addressInfo = JSON.stringify(
    addressFields.reduce((acc, field) => {
      acc[field] = get(info, field)
      return acc
    }, {})
  )

  const isDirectBuy = goodsId && skuId && goodsNum

  try {
    showLoadingToast()
    orderType.value = isDirectBuy ? ORDER_TYPE.BUY_NOW : ORDER_TYPE.CART

    let res, json

    if (isDirectBuy) {
      [res, json] = await getBuyNowGoods({
        goodsId,
        skuId,
        goodsNum,
        addressInfo,
        bizCode: getBizCode('ORDER')
      })
    } else {
      const buyGoodsList = buyProductCart.get() || buyProductCartSession.get()
      if (!buyGoodsList) {
        console.error('buyProductCart购车下单缓存数据为空')
      }

      [res, json] = await getSelectedGoods({
        bizCode: getBizCode('ORDER'),
        addressInfo,
        buyGoodsList: JSON.stringify(buyGoodsList)
      })
    }

    closeToast()

    if (res.code !== '0000') {
      isSubmit.value = false
      showToast(res.msg)
    } else {
      isSubmit.value = true
      processBuyNowData(json)
      if (isJD.value && totalFreight.value) {
        showToast('运费已分摊至商品金额')
      }
    }
  } catch (error) {
    closeToast()
    // console.error('重新计算订单失败:', error)
    showToast('重新计算订单失败')
  }

  await checkRegionalRestrictions()
}

const handleCreateAddress = () => {
  router.push('/addr/add')
}

const handleEditAddress = (address) => {
  router.push(`/addr/edit/${address.addressId}`)
}

const handleShowQuotaPopup = async (event) => {
  if (isZqBiz.value) return

  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  const availableActivityQuotaList = get(availableActivityQuota.value, 'quotaInfo', [])

  if (!isEmpty(availableActivityQuotaList)) {
    showQuotaPopup.value = true
    return
  }

  const params = {
    bizCode: getBizCode(),
    orderPrice: parseInt(new Decimal(cartPriceTotal.value).mul(new Decimal(100)).toString())
  }

  showQuotaPopup.value = true
  showLoadingToast()

  try {
    const [err, json] = await queryAmount(params)
    closeToast()

    if (!err) {
      availableActivityQuota.value = json
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    // console.error('获取慰问活动数据失败:', error)
    showToast('获取活动信息失败')
  }
}

const getPaymentDetails = async (orderPrice = null) => {
  if (isZqBiz.value) return

  const priceToUse = orderPrice || cartPriceTotal.value
  if (priceToUse === 0) return

  showLoadingToast()
  const params = {
    bizCode: getBizCode(),
    orderPrice: parseInt(new Decimal(priceToUse).mul(new Decimal(100)).toString())
  }

  const [err, json] = await queryAmount(params)
  closeToast()

  if (!err) {
    availableActivityQuota.value = json
    const grantAmount = get(json, 'grantAmount', 0)
    const balanceAmount = get(json, 'amount', 0)
    const realPrice = priceToUse * 100

    if (grantAmount > 0) {
      const activityName = get(json, 'activityName', '')
      activeName.value = activityName

      if (!activityName) {
        quotaPayment.value = fenToYuan(0)
        selfPayment.value = fenToYuan(realPrice)
        return
      }

      if (realPrice > balanceAmount) {
        quotaPayment.value = fenToYuan(balanceAmount)
        selfPayment.value = fenToYuan(realPrice - balanceAmount)
      } else {
        quotaPayment.value = fenToYuan(realPrice)
        selfPayment.value = fenToYuan(0)
      }
    } else {
      const availableActivityQuotaList = get(availableActivityQuota.value, 'quotaInfo', [])
      const activityName = get(availableActivityQuotaList, '[0].activityName', '')
      activeName.value = activityName

      if (!activityName) {
        quotaPayment.value = fenToYuan(0)
        selfPayment.value = fenToYuan(realPrice)
        return
      }

      if (!isEmpty(availableActivityQuotaList)) {
        const { balanceAmount } = availableActivityQuotaList[0]
        if (realPrice > balanceAmount) {
          quotaPayment.value = fenToYuan(balanceAmount)
          selfPayment.value = fenToYuan(realPrice - balanceAmount)
        } else {
          quotaPayment.value = fenToYuan(realPrice)
          selfPayment.value = fenToYuan(0)
        }
      }
    }
  } else {
    showToast(err.msg)
  }
}

const handleQuotaChange = async (tabIndex) => {
  if (isZqBiz.value) return

  const params = {
    bizCode: getBizCode(),
    orderPrice: parseInt(new Decimal(cartPriceTotal.value).mul(new Decimal(100)).toString())
  }

  const availableActivityQuotaList = get(availableActivityQuota.value, 'quotaInfo', [])
  const unAvailableActivityQuotaList = get(unAvailableActivityQuota.value, 'quotaInfo', [])

  if (tabIndex === 0 && availableActivityQuotaList && availableActivityQuotaList.length > 0) {
    return
  }

  if (tabIndex === 1 && unAvailableActivityQuotaList && unAvailableActivityQuotaList.length > 0) {
    return
  }

  if (tabIndex === 0) {
    showLoadingToast()
    const [err, json] = await queryAmount(params)
    closeToast()
    if (!err) {
      availableActivityQuota.value = json
    } else {
      showToast(err.msg)
    }
  } else {
    showLoadingToast()
    const [err, json] = await notApplicableQuotaWish(params)
    closeToast()
    if (!err) {
      unAvailableActivityQuota.value = json
    } else {
      showToast(err.msg)
    }
  }
}

const handleSubmitOrder = throttle(async () => {
  if (!isSubmit.value) return

  const curAddrInfo = userStore.curAddressInfo

  if (curAddrInfo.type === 2 || curAddrInfo.type === 3) {
    $alert({
      message: curAddrInfo.type === 2
        ? '您还未选择收货地址，是否新建收货地址？'
        : `您在浏览中选择${curAddrInfo.provinceName}${curAddrInfo.cityName}，是否新建收货地址？`,
      confirmButtonText: '新建地址',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: () => {
        router.push({ name: 'address-add', query: curAddrInfo })
      },
      onCancelCallback: () => {
        userStore.setTempAddr(null)
      }
    })
    return
  }

  const hasUnsupportedGoods = orderCartList.value.some(supplier =>
    get(supplier, 'goodsList', []).some(skuInfo => get(skuInfo, 'nosale'))
  )

  if (hasUnsupportedGoods) {
    showToast('当前部分商品在所选地区暂不支持销售，请更改收货地址或者返回重新选择商品')
    return
  }

  let currentDeveloperId = ''
  if (getBizCode() === 'fulihui') {
    currentDeveloperId = curDeveloperId.get()
    if (!currentDeveloperId) {
      showLoadingToast()
      try {
        const [err, json] = await getFuLiHuiID({ bizCode: getBizCode('QUERY') })
        closeToast()
        if (!err) {
          currentDeveloperId = json || ''
          curDeveloperId.set(currentDeveloperId)
        }
      } catch (error) {
        closeToast()
        // console.error('获取福利汇ID失败:', error)
      }
    }
  }

  if (!isRemoveGifts.value) {
    const cartGoodsList = orderCartList.value.flatMap(supplier =>
      get(supplier, 'goodsList', []).map(skuInfo => ({
        cartGoodsId: get(skuInfo, 'rawData.cartGoodsId'),
        cartSkuId: get(skuInfo, 'rawData.cartSkuId'),
        skuNum: get(skuInfo, 'quantity'),
        supplierCode: get(skuInfo, 'rawData.supplierCode'),
        skuNeedGift: true
      }))
    )

    orderVoInfo.value = {
      disriBiz: getBizCode('ORDER'),
      bizChannelCode: curChannelBiz.get(),
      addressInfo: { ...curAddrInfo },
      developerInfo: {
        developerId: developerId.value || currentDeveloperId,
        proxyChannel: ''
      },
      cartGoodsList,
      isDirectPay: null
    }
  }

  if (orderType.value === ORDER_TYPE.BUY_NOW) {
    orderVoInfo.value.isDirectPay = true
  } else if (orderType.value === ORDER_TYPE.CART) {
    orderVoInfo.value.isDirectPay = false
  } else {
    return
  }

  orderVoInfo.value.bizChannelCode = curChannelBiz.get()

  const bondParams = ['curSelectedMoney', 'curSelectedTime', 'orderNo']
  bondParams.forEach(param => {
    const value = get(route.query, param)
    if (value) {
      const mapping = {
        curSelectedMoney: 'bondPrice',
        curSelectedTime: 'bondTerm',
        orderNo: 'bondOrderId'
      }
      orderVoInfo.value[mapping[param]] = param === 'curSelectedTime' ? (+value) * 12 : +value
    }
  })

  try {
    showLoadingToast()
    const [err, json] = await submitOrder({
      orderVoInfo: JSON.stringify(orderVoInfo.value)
    })
    closeToast()

    if (!err) {
      if (getBizCode() === 'fupin' && get(json, 'isNeedCompanyInsert') === 'true') {
        dialogShow.value = true
        wapay.value = {
          encryptContent: get(json, 'encryptContent', ''),
          wapURL: get(json, 'wapURL', ''),
          bizOrderId: get(json, 'storeOrderId', '')
        }
      } else {
        formSubmit(get(json, 'wapURL'), { param: get(json, 'encryptContent') })
        if (get(orderVoInfo.value, 'isDirectPay')) {
          buyProductNow.set('')
          buyProductNowSession.set('')
        }
      }
    } else {
      if (get(err, 'code') === '3008') {
        const removeGifts = () => {
          orderCartList.value.forEach(supplier =>
            get(supplier, 'goodsList', []).forEach(product => {
              if (get(product, 'rawData.cartSkuId') === get(err, 'data')) {
                const skuList = get(product, 'rawData.goods.skuList', [])
                skuList.forEach(item => {
                  item.giftList = []
                })
              }
            })
          )

          get(orderVoInfo.value, 'cartGoodsList', []).forEach(item => {
            if (get(item, 'cartSkuId') === get(err, 'data')) {
              item.skuNeedGift = false
            }
          })
          isRemoveGifts.value = true
        }

        $alert({
          message: `您购买的商品: ${get(err, 'msg')}。赠品已赠完，是否继续购买？`,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
          messageAlign: 'left',
          onConfirmCallback: removeGifts,
          onCancelCallback: () => { }
        })
      } else {
        showToast(get(err, 'msg'))
      }
    }
  } catch (error) {
    closeToast()
    // console.error('提交订单失败:', error)
    showToast('提交订单失败，请重试')
  }
}, 1000)

onMounted(async () => {
  await initOrderData()
})
</script>

<style scoped lang="less">
.order-confirm {
  min-height: 100vh;
  background-color: @bg-color-gray;
  padding: 8px 10px;
  box-sizing: border-box;

  &__goods {
    margin-bottom: 8px;
  }

  &__summary {
    margin-bottom: 8px;

    :deep(.order-confirm__activity-text) {
      color: @color-orange;
      font-weight: @font-weight-500;

      &--empty {
        color: @text-color-tertiary;
        font-weight: @font-weight-400;
      }
    }
  }

  &__submit {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;

    &-info {
      flex: 1;
      min-width: 0;
    }

    &-price {
      font-size: @font-size-14;
      color: @text-color-primary;
      display: block;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    &-details {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      span {
        font-size: @font-size-13;
        color: @text-color-tertiary;
        line-height: 1.4;
        white-space: nowrap;
      }
    }
  }
}

* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
</style>
